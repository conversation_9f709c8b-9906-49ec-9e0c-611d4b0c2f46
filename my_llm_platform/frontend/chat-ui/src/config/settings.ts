/**
 * 設定オブジェクト
 * 環境変数とデフォルト値を統一的に管理
 */

// 環境変数からの値取得とデフォルト値の設定
const getEnvValue = (key: string, defaultValue: string): string => {
  const value = import.meta.env[`VITE_${key}`];
  return value !== undefined ? String(value) : defaultValue;
};

// 環境変数からブール値を取得
const getBooleanEnvValue = (key: string, defaultValue: boolean): boolean => {
  const value = import.meta.env[`VITE_${key}`];
  if (value === undefined) return defaultValue;
  return value === 'true' || value === '1' || value === 'yes';
};

// 設定オブジェクト
export const settings = {
  // API設定
  API_BASE_URL: getEnvValue('API_BASE_URL', 'http://localhost:8000'),
  API_PREFIX: getEnvValue('API_PREFIX', ''),
  
  // モデル設定
  DEFAULT_MODEL: getEnvValue('DEFAULT_MODEL', 'llama3'),
  DEFAULT_BACKEND: getEnvValue('DEFAULT_BACKEND', 'ollama'),
  
  // 認証設定
  AUTH_MODE: getEnvValue('AUTH_MODE', 'none'),
  
  // プラグイン設定
  ENABLE_PLUGINS: getBooleanEnvValue('ENABLE_PLUGINS', false),
  
  // UI設定
  LANGUAGE: getEnvValue('LANGUAGE', 'ja'),
  THEME: getEnvValue('THEME', 'light'),
  
  // デバッグ設定
  DEBUG: getBooleanEnvValue('DEBUG', false),
};

// 設定オブジェクトの読み取り専用エクスポート
export default Object.freeze(settings);
