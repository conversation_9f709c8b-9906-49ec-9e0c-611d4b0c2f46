"""
LLM推論クライアント - 異なるバックエンドに接続するための統一インターフェース
このモジュールは、すべてのLLM操作の公開APIを提供します。
"""

import logging
import os
from enum import Enum
from typing import Optional, Union, Dict, Any, List, AsyncGenerator
from openai import OpenAI, AsyncOpenAI
from ..config import settings, get_versioned_url

logger = logging.getLogger("llm_client")

class BackendType(Enum):
    """
    サポートされているLLMバックエンドタイプ
    """
    OPENAI = "openai"
    AZURE = "azure"
    OLLAMA = "ollama"
    VLLM = "vllm"
    SGLANG = "sglang"
    XINFERENCE = "xinference"
    CUSTOM = "custom"
    
    @classmethod
    def from_string(cls, backend_name: str) -> 'BackendType':
        """文字列からバックエンドタイプを取得"""
        try:
            return cls(backend_name.lower())
        except ValueError:
            logger.warning(f"未知のバックエンド名: {backend_name}, デフォルトのCUSTOMを使用")
            return cls.CUSTOM

class LLMClient:
    """
    LLMクライアント - OpenAIクライアントを使用して異なるバックエンドに接続します
    """
    
    @staticmethod
    def get_client(backend_type: Union[BackendType, str, None] = None, model: Optional[str] = None, api_key: Optional[str] = None) -> Union[OpenAI, AsyncOpenAI]:
        """
        指定されたバックエンドタイプとモデルに基づいてOpenAIクライアントを取得します。
        """
        # モデルが指定されている場合、そのモデルに対応するバックエンドタイプを取得
        if model and not backend_type:
            backend_type = LLMInterface.get_backend_type(model)
        
        # 文字列からバックエンドタイプに変換
        if isinstance(backend_type, str):
            backend_type = BackendType.from_string(backend_type)
        
        # バックエンドタイプが指定されていない場合、デフォルトを使用
        if not backend_type:
            default_backend = settings.DEFAULT_LLM_BACKEND
            backend_type = BackendType.from_string(default_backend)
        
        # APIキーが指定されていない場合、設定から取得
        if not api_key:
            if backend_type == BackendType.OPENAI:
                api_key = settings.OPENAI_API_KEY
            elif backend_type == BackendType.AZURE:
                api_key = settings.AZURE_API_KEY
            else:
                # その他のバックエンドの場合、ダミーキーを使用
                api_key = "dummy-api-key"
        
        # バックエンドタイプに基づいてベースURLを取得
        base_url = None
        if backend_type == BackendType.OPENAI:
            base_url = settings.OPENAI_API_BASE
        elif backend_type == BackendType.AZURE:
            base_url = settings.AZURE_API_BASE
        elif backend_type == BackendType.OLLAMA:
            base_url = settings.OLLAMA_ENDPOINT
        elif backend_type == BackendType.VLLM:
            base_url = settings.VLLM_ENDPOINT
        elif backend_type == BackendType.SGLANG:
            base_url = settings.SGLANG_ENDPOINT
        elif backend_type == BackendType.XINFERENCE:
            base_url = settings.XINFERENCE_ENDPOINT
        elif backend_type == BackendType.CUSTOM:
            base_url = settings.CUSTOM_LLM_ENDPOINT
        
        # バージョン付きURLを取得（必要な場合）
        if base_url:
            base_url = get_versioned_url(base_url)
        
        logger.debug(f"OpenAIクライアント作成: backend_type={backend_type}, base_url={base_url}")
        
        # OpenAIクライアントを作成して返す
        return AsyncOpenAI(api_key=api_key, base_url=base_url)
    
    @staticmethod
    def get_client_for_model(model: str, api_key: Optional[str] = None) -> Union[OpenAI, AsyncOpenAI]:
        """
        指定されたモデルに基づいてOpenAIクライアントを取得します。
        
        引数:
            model: モデル名
            api_key: APIキー（オプション）
            
        戻り値:
            OpenAIクライアントのインスタンス
        """
        return LLMClient.get_client(model=model, api_key=api_key)

class LLMInterface:
    """
    LLMインターフェース - すべてのLLM操作の統一エントリーポイント
    """
    
    @staticmethod
    def get_backend_type(model_name: str) -> BackendType:
        """モデル名からバックエンドタイプを取得"""
        # モデルマッピングからバックエンド名を取得
        backend_name = settings.MODEL_BACKEND_MAPPING.get(model_name, settings.DEFAULT_LLM_BACKEND)
        return BackendType.from_string(backend_name)
    
    @staticmethod
    async def chat_completion(
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        stream: bool = False,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Any] = None,
    ) -> Union[Dict[str, Any], AsyncGenerator[Dict[str, Any], None]]:
        """チャット完了リクエストを処理（バックエンドに応じて適切な方法で）"""
        backend_type = LLMInterface.get_backend_type(model)
        
        # vLLMバックエンドの場合は直接APIを使用
        if backend_type == BackendType.VLLM:
            from .vllm_direct import vllm_chat_completion, vllm_stream_chat_completion
            if stream:
                return vllm_stream_chat_completion(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p,
                    tools=tools,
                    tool_choice=tool_choice
                )
            else:
                return await vllm_chat_completion(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p,
                    tools=tools,
                    tool_choice=tool_choice
                )
        
        # その他のバックエンドはOpenAIクライアントを使用
        client = LLMClient.get_client(model=model)
        return await client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            stream=stream,
            tools=tools,
            tool_choice=tool_choice
        )
    
    @staticmethod
    async def completion(
        model: str,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        stream: bool = False,
    ) -> Union[Dict[str, Any], AsyncGenerator[Dict[str, Any], None]]:
        """テキスト補完リクエストを処理"""
        backend_type = LLMInterface.get_backend_type(model)
        
        # vLLMバックエンドの場合は直接APIを使用
        if backend_type == BackendType.VLLM:
            from .vllm_direct import vllm_completion, vllm_stream_completion
            if stream:
                return vllm_stream_completion(
                    model=model,
                    prompt=prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p
                )
            else:
                return await vllm_completion(
                    model=model,
                    prompt=prompt,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p
                )
        
        # その他のバックエンドはOpenAIクライアントを使用
        client = LLMClient.get_client(model=model)
        return await client.completions.create(
            model=model,
            prompt=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            stream=stream
        )
    
    @staticmethod
    async def embeddings(
        model: str,
        input_text: Union[str, List[str]],
        dimensions: Optional[int] = None
    ) -> Dict[str, Any]:
        """埋め込みリクエストを処理"""
        client = LLMClient.get_client(model=model)
        return await client.embeddings.create(
            model=model,
            input=input_text,
            dimensions=dimensions
        )

# 公開API - これらの関数のみを外部から使用
get_backend_type = LLMInterface.get_backend_type
chat_completion = LLMInterface.chat_completion
completion = LLMInterface.completion
embeddings = LLMInterface.embeddings
get_client = LLMClient.get_client  # 下位互換性のため
get_client_for_model = LLMClient.get_client_for_model  # 下位互換性のため
