"""
LLM推論クライアント
OpenAIクライアントライブラリを使用して、異なるバックエンドに接続するためのシンプルなラッパー
"""

import logging
from enum import Enum
from typing import Optional, Union
from openai import OpenAI
from ..config import settings, get_versioned_url


class BackendType(Enum):
    """
    LLMバックエンドタイプの列挙型
    """
    OLLAMA = "ollama"
    VLLM = "vllm"
    SGLANG = "sglang"
    X_INFERENCE = "x-inference"
    OPENAI = "openai"

    @classmethod
    def from_string(cls, backend_type: str) -> 'BackendType':
        """
        文字列からBackendTypeを取得します。
        不明な値の場合はOLLAMAを返します。
        """
        try:
            return cls(backend_type.lower())
        except (ValueError, AttributeError):
            logging.warning(f"不明なバックエンドタイプ: {backend_type}、OLLAMAを使用します")
            return cls.OLLAMA

class LLMClient:
    """
    LLMクライアント - OpenAIクライアントを使用して異なるバックエンドに接続します
    """

    @staticmethod
    def get_client(backend_type: Union[BackendType, str, None] = None, model: Optional[str] = None, api_key: Optional[str] = None) -> OpenAI:
        """
        指定されたバックエンドタイプとモデルに基づいてOpenAIクライアントを取得します。

        引数:
            backend_type: バックエンドタイプ（BackendType列挙型または文字列）
                         Noneの場合、settings.DEFAULT_LLM_BACKENDを使用
                         例: BackendType.OLLAMA または "ollama"
            model: モデル名（Noneの場合、モデルに基づいてバックエンドを選択）
            api_key: APIキー（OpenAIの場合は必須、他のバックエンドではダミー値でOK）

        戻り値:
            OpenAIクライアントのインスタンス
        """
        # モデルに基づいてバックエンドを選択
        backend_type_str = None

        if isinstance(backend_type, BackendType):
            backend_type_str = backend_type.value
        elif isinstance(backend_type, str):
            backend_type_str = backend_type.lower()

        if model and not backend_type_str:
            if hasattr(settings, 'MODEL_BACKEND_MAPPING') and settings.MODEL_BACKEND_MAPPING:
                backend_type_str = settings.MODEL_BACKEND_MAPPING.get(model)

        # バックエンドタイプが指定されていない場合、デフォルトを使用
        if not backend_type_str:
            backend_type_str = settings.DEFAULT_LLM_BACKEND

        # BackendType列挙型に変換
        backend_enum = BackendType.from_string(backend_type_str)

        # バックエンドタイプに基づいてベースURLを取得
        base_url = LLMClient._get_base_url(backend_enum)

        # APIキーを設定
        if not api_key:
            if backend_enum == BackendType.OPENAI:
                api_key = settings.OPENAI_API_KEY
            else:
                api_key = "dummy-api-key"  # ローカルバックエンドの場合はダミー値で十分

        # 追加のヘッダーを設定
        headers = {}

        # vLLMの場合の特別な処理
        if backend_enum == BackendType.VLLM:
            # 注意: チャットテンプレートはHTTPヘッダーに設定できません（特殊文字や改行の制限があるため）
            # vLLMサーバー側でデフォルトのテンプレートを使用するか、別の方法でテンプレートを適用する必要があります
            logging.info(f"vLLMバックエンドを使用します（デフォルトのチャットテンプレートを使用）")

        # OpenAIクライアントを作成して返す
        try:
            client = OpenAI(
                base_url=base_url,
                api_key=api_key,
                default_headers=headers
            )
            logging.info(f"OpenAIクライアントを初期化しました: {base_url}, バックエンド: {backend_enum.value}")
            return client
        except Exception as e:
            logging.error(f"OpenAIクライアントの初期化エラー: {e}")
            raise

    @staticmethod
    def _get_base_url(backend_type: BackendType) -> str:
        """
        バックエンドタイプに基づいてベースURLを取得します。

        各バックエンドはOpenAI互換のエンドポイントを提供する必要があります。
        APIバージョンは設定に基づいて動的に追加されます。
        """
        if backend_type == BackendType.OLLAMA:
            # Ollamaはhttp://localhost:11434/v1/chatのようなOpenAI互換エンドポイントを提供
            return get_versioned_url(settings.OLLAMA_ENDPOINT)
        elif backend_type == BackendType.VLLM:
            return get_versioned_url(settings.VLLM_ENDPOINT)
        elif backend_type == BackendType.SGLANG:
            return get_versioned_url(settings.SGLANG_ENDPOINT)
        elif backend_type == BackendType.X_INFERENCE:
            return get_versioned_url(settings.X_INFERENCE_ENDPOINT)
        elif backend_type == BackendType.OPENAI:
            return get_versioned_url(settings.OPENAI_ENDPOINT)
        else:
            # デフォルトはOllamaを使用し、OpenAI互換エンドポイントを使用
            logging.warning(f"不明なバックエンドタイプ: {backend_type}、Ollamaを使用します")
            return get_versioned_url(settings.OLLAMA_ENDPOINT)

# 便利な関数
def get_client(backend_type: Union[BackendType, str, None] = None, model: Optional[str] = None, api_key: Optional[str] = None) -> OpenAI:
    """
    指定されたバックエンドタイプとモデルに基づいてOpenAIクライアントを取得します。
    LLMClient.get_clientのショートカット。

    引数:
        backend_type: バックエンドタイプ（BackendType列挙型または文字列）
                     例: BackendType.OLLAMA または "ollama"
        model: モデル名
        api_key: APIキー
    """
    return LLMClient.get_client(backend_type, model, api_key)

def get_client_for_model(model: str, api_key: Optional[str] = None) -> OpenAI:
    """
    指定されたモデルに基づいてOpenAIクライアントを取得します。

    引数:
        model: モデル名
        api_key: APIキー
    """
    return LLMClient.get_client(model=model, api_key=api_key)
