"""
OpenAI互換API - OpenAI APIと互換性のあるエンドポイントを提供します
"""

from fastapi import APIRouter, HTTPException, Depends, Request, Response, UploadFile, File, Form, Body, Query, Path
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field, model_validator
from typing import List, Optional, Dict, Any, Union, Literal, Annotated
import json
import logging
import time
import os
import uuid
import asyncio
import traceback
from datetime import datetime
from sqlalchemy.orm import Session
from ..config import settings
from ..auth import get_current_user
from ..inference.llm_client import get_client, BackendType
from ..inference.vllm_direct import vllm_chat_completion, vllm_completion, vllm_stream_completion
from ..utils.response_filter import filter_streaming_chunk, filter_chat_completion_response, filter_completion_response
from openai import NotFoundError

# ロガーの設定
logger = logging.getLogger("openai_api")

# ルーターの作成
router = APIRouter(tags=["OpenAI Compatible API"])

# ヘルパー関数
def get_backend_type(model: str) -> BackendType:
    """モデル名からバックエンドタイプを取得"""
    # モデルマッピングから取得
    if hasattr(settings, 'MODEL_BACKEND_MAPPING') and settings.MODEL_BACKEND_MAPPING:
        backend_str = settings.MODEL_BACKEND_MAPPING.get(model)
        if backend_str:
            return BackendType.from_string(backend_str)

    # デフォルトバックエンドを返す
    return BackendType.from_string(settings.DEFAULT_LLM_BACKEND)

# モデル定義
# チャット完了リクエスト用のモデル
class ChatCompletionMessage(BaseModel):
    role: str
    content: str
    name: Optional[str] = None

    @model_validator(mode='before')
    @classmethod
    def validate_role(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        allowed_roles = ["system", "user", "assistant", "function", "tool"]
        if "role" in data and data["role"] not in allowed_roles:
            raise ValueError(f"roleは次のいずれかである必要があります: {', '.join(allowed_roles)}")
        return data

class ChatCompletionFunctionCall(BaseModel):
    name: str
    arguments: str

class ChatCompletionTool(BaseModel):
    type: str = "function"
    function: Dict[str, Any]

class ChatCompletionToolChoice(BaseModel):
    type: str = "function"
    function: Dict[str, Any]

class CompletionRequest(BaseModel):
    model: str = Field(..., description="使用するモデル名")
    prompt: str = Field(..., description="補完するためのプロンプト")
    suffix: Optional[str] = None
    temperature: Optional[float] = 1.0
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    max_tokens: Optional[int] = 16
    stop: Optional[Union[str, List[str]]] = None
    stream: Optional[bool] = False
    logprobs: Optional[int] = None
    echo: Optional[bool] = False
    user: Optional[str] = None

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatCompletionMessage]
    temperature: Optional[float] = 0.7
    top_p: Optional[float] = 1.0
    top_k: Optional[int] = 50
    n: Optional[int] = 1
    max_tokens: Optional[int] = 2048
    stream: Optional[bool] = False
    presence_penalty: Optional[float] = 0.0
    frequency_penalty: Optional[float] = 0.0
    stop: Optional[Union[str, List[str]]] = None
    tools: Optional[List[ChatCompletionTool]] = None
    tool_choice: Optional[Union[str, ChatCompletionToolChoice]] = None
    user: Optional[str] = None
    response_format: Optional[Dict[str, str]] = None
    seed: Optional[int] = None

    @model_validator(mode='before')
    @classmethod
    def validate_messages(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        messages = data.get("messages")
        if not messages:
            raise ValueError("messagesは必須で、少なくとも1つのメッセージを含む必要があります")
        if not isinstance(messages, list):
            raise ValueError("messagesはリスト形式である必要があります")
        if len(messages) < 1:
            raise ValueError("messagesは少なくとも1つのメッセージを含む必要があります")
        return data

    @model_validator(mode='after')
    def validate_temperature(self) -> 'ChatCompletionRequest':
        if self.temperature is not None and (self.temperature < 0 or self.temperature > 2):
            raise ValueError("temperatureは0から2の間である必要があります")
        return self

    @model_validator(mode='after')
    def validate_top_p(self) -> 'ChatCompletionRequest':
        if self.top_p is not None and (self.top_p < 0 or self.top_p > 1):
            raise ValueError("top_pは0から1の間である必要があります")
        return self

# 埋め込みリクエスト用のモデル
class EmbeddingRequest(BaseModel):
    model: str
    input: Union[str, List[str]]
    encoding_format: Optional[str] = "float"
    user: Optional[str] = None
    dimensions: Optional[int] = None

# 音声文字起こしリクエスト用のモデル
class AudioTranscriptionRequest(BaseModel):
    file: UploadFile = File(...)
    model: str = Form(...)
    prompt: Optional[str] = Form(None)
    response_format: Optional[str] = Form("json")
    temperature: Optional[float] = Form(0.0)
    language: Optional[str] = Form(None)

# 画像生成リクエスト用のモデル
class ImageGenerationRequest(BaseModel):
    prompt: str
    model: Optional[str] = "dall-e-3"
    n: Optional[int] = 1
    size: Optional[str] = "1024x1024"
    response_format: Optional[str] = "url"
    style: Optional[str] = "vivid"
    quality: Optional[str] = "standard"
    user: Optional[str] = None

# モデレーションリクエスト用のモデル
class ModerationRequest(BaseModel):
    input: Union[str, List[str]]
    model: Optional[str] = "text-moderation-latest"

# ファイルアップロードリクエスト用のモデル
class FileUploadRequest(BaseModel):
    file: UploadFile = File(...)
    purpose: str = Form(...)

# ファインチューニングジョブリクエスト用のモデル
class FineTuningJobRequest(BaseModel):
    training_file: str
    validation_file: Optional[str] = None
    model: str
    hyperparameters: Optional[Dict[str, Any]] = None
    suffix: Optional[str] = None

# スレッド作成リクエスト用のモデル
class ThreadCreateRequest(BaseModel):
    messages: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None

# スレッドメッセージ作成リクエスト用のモデル
class ThreadMessageCreateRequest(BaseModel):
    role: str
    content: str
    file_ids: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

# スレッド実行リクエスト用のモデル
class ThreadRunCreateRequest(BaseModel):
    assistant_id: str
    model: Optional[str] = None
    instructions: Optional[str] = None
    tools: Optional[List[Dict[str, Any]]] = None
    metadata: Optional[Dict[str, Any]] = None

# エラーフォーマット関数
def format_openai_error(status_code: int, message: str, type_: str, param: Optional[str] = None, code: Optional[str] = None) -> JSONResponse:
    """OpenAI形式のエラーレスポンスを作成"""
    error_detail = {
        "message": message,
        "type": type_,
    }
    if param:
        error_detail["param"] = param
    if code:
        error_detail["code"] = code

    return JSONResponse(
        status_code=status_code,
        content={"error": error_detail}
    )

# ストリーミングジェネレーター関数
async def chat_completion_stream_generator(model, messages, temperature, max_tokens, top_p, tools=None, tool_choice=None):
    """チャット完了ストリーミングジェネレーター"""
    try:
        # 適切なクライアントを取得
        client = get_client(model=model)

        # リクエストパラメータを準備
        params = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": top_p,
            "stream": True
        }

        # ツールが存在する場合は追加
        if tools:
            params["tools"] = tools
            params["tool_choice"] = tool_choice

        # ストリーミングレスポンスを取得
        stream = client.chat.completions.create(**params)

        async for chunk in stream:
            # ChatCompletionChunkオブジェクトをdictに変換
            if hasattr(chunk, 'model_dump'):
                chunk_dict = chunk.model_dump()
            else:
                # 辞書形式に変換
                chunk_dict = {
                    "id": getattr(chunk, "id", str(uuid.uuid4())),
                    "object": "chat.completion.chunk",
                    "created": getattr(chunk, "created", int(time.time())),
                    "model": model,
                    "choices": []
                }
                
                # choicesの処理
                if hasattr(chunk, "choices") and chunk.choices:
                    for choice in chunk.choices:
                        choice_dict = {
                            "index": getattr(choice, "index", 0),
                            "delta": {},
                            "finish_reason": getattr(choice, "finish_reason", None)
                        }
                        
                        # deltaの処理
                        if hasattr(choice, "delta"):
                            delta = choice.delta
                            if hasattr(delta, "content") and delta.content is not None:
                                choice_dict["delta"]["content"] = delta.content
                            if hasattr(delta, "role") and delta.role is not None:
                                choice_dict["delta"]["role"] = delta.role
                            if hasattr(delta, "tool_calls") and delta.tool_calls:
                                choice_dict["delta"]["tool_calls"] = [
                                    {
                                        "id": tool_call.id,
                                        "type": tool_call.type,
                                        "function": {
                                            "name": tool_call.function.name,
                                            "arguments": tool_call.function.arguments
                                        }
                                    } for tool_call in delta.tool_calls
                                ]
                        
                        chunk_dict["choices"].append(choice_dict)
            
            # ストリーミングチャンクをフィルタリング（特殊トークンの削除）
            filtered_chunk = filter_streaming_chunk(chunk_dict, is_chat_completion=True)
            yield f"data: {json.dumps(filtered_chunk)}\n\n"
        yield "data: [DONE]\n\n"
    except Exception as e:
        logger.error(f"ストリーミング中エラー: {e}")
        logger.error(f"エラー詳細: {traceback.format_exc()}")
        error_data = {"error": {"message": str(e), "type": "server_error"}}
        yield f"data: {json.dumps(error_data)}\n\n"

async def completion_stream_generator(model, prompt, temperature, max_tokens, top_p):
    """テキスト補完ストリーミングジェネレーター"""
    try:
        # 適切なクライアントを取得
        client = get_client(model=model)

        # ストリーミングレスポンスを取得
        stream = client.completions.create(
            model=model,
            prompt=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            stream=True
        )

        async for chunk in stream:
            # CompletionChunkオブジェクトをdictに変換
            if hasattr(chunk, 'model_dump'):
                chunk_dict = chunk.model_dump()
            else:
                # 辞書形式に変換
                chunk_dict = {
                    "id": getattr(chunk, "id", str(uuid.uuid4())),
                    "object": "text_completion",
                    "created": getattr(chunk, "created", int(time.time())),
                    "model": model,
                    "choices": []
                }
                
                # choicesの処理
                if hasattr(chunk, "choices") and chunk.choices:
                    for choice in chunk.choices:
                        choice_dict = {
                            "index": getattr(choice, "index", 0),
                            "text": getattr(choice, "text", ""),
                            "finish_reason": getattr(choice, "finish_reason", None)
                        }
                        chunk_dict["choices"].append(choice_dict)
            
            # ストリーミングチャンクをフィルタリング（特殊トークンの削除）
            filtered_chunk = filter_streaming_chunk(chunk_dict, is_chat_completion=False)
            yield f"data: {json.dumps(filtered_chunk)}\n\n"
        yield "data: [DONE]\n\n"
    except Exception as e:
        logger.error(f"ストリーミング中エラー: {e}")
        logger.error(f"エラー詳細: {traceback.format_exc()}")
        error_data = {"error": {"message": str(e), "type": "server_error"}}
        yield f"data: {json.dumps(error_data)}\n\n"

# チャット完了API
@router.post("/v1/chat/completions")
async def create_chat_completion(
    request_body: ChatCompletionRequest,
    request: Request,  # FastAPI依存注入用
    current_user = Depends(get_current_user)  # 認証用依存注入
):
    """チャット完了APIエンドポイント

    OpenAI互換のチャット完了APIを提供します。
    ストリーミングと非ストリーミングの両方のレスポンスをサポートします。
    """
    # 未使用パラメータの警告を抑制
    _ = request, current_user
    logger.info(f"チャット完了リクエスト: model={request_body.model}, stream={request_body.stream}")
    
    try:
        # メッセージを準備
        messages = [{"role": msg.role, "content": msg.content} for msg in request_body.messages]
        
        # ツールを準備（存在する場合）
        tools = None
        if request_body.tools:
            tools = [tool.model_dump() for tool in request_body.tools]
        
        # ストリーミングリクエストの場合
        if request_body.stream:
            # ストリーミングレスポンスを返す
            return StreamingResponse(
                chat_completion_stream_generator(
                    model=request_body.model,
                    messages=messages,
                    temperature=request_body.temperature,
                    max_tokens=request_body.max_tokens,
                    top_p=request_body.top_p,
                    tools=tools,
                    tool_choice=request_body.tool_choice
                ),
                media_type="text/event-stream"
            )
        
        # 非ストリーミングリクエストの場合
        else:
            # バックエンドタイプを取得
            backend_type = get_backend_type(request_body.model)

            # vLLMバックエンドの場合は直接vLLMサーバーにリクエストを送信
            if backend_type == BackendType.VLLM:
                logger.info(f"vLLMバックエンドを使用します（直接APIを呼び出し）")

                # vLLMサーバーに直接リクエストを送信
                response = await vllm_chat_completion(
                    model=request_body.model,
                    messages=messages,
                    temperature=request_body.temperature,
                    max_tokens=request_body.max_tokens,
                    top_p=request_body.top_p,
                    tools=tools,
                    tool_choice=request_body.tool_choice
                )

                # レスポンスをフィルタリング（特殊トークンの削除と最初の有効な回答の選択）
                # 注意: vllm_chat_completion内ですでにフィルタリングされているが、念のため再度フィルタリング
                response = filter_chat_completion_response(response)

                # レスポンスの内容をログに出力
                if "choices" in response and len(response["choices"]) > 0:
                    if "message" in response["choices"][0] and "content" in response["choices"][0]["message"]:
                        content = response["choices"][0]["message"]["content"]
                        logger.info(f"フィルタリング後のレスポンス内容: {content[:100]}...")
            else:
                # その他のバックエンドの場合はOpenAIクライアントを使用
                # 適切なバックエンドクライアントを取得
                client = get_client(model=request_body.model)
                if not client:
                    raise ValueError(f"モデル {request_body.model} に対応するクライアントが見つかりません")

                # リクエストパラメータを準備
                params = {
                    "model": request_body.model,
                    "messages": messages,
                    "temperature": request_body.temperature,
                    "max_tokens": request_body.max_tokens,
                    "top_p": request_body.top_p,
                    "stream": False
                }

                # ツールが存在する場合は追加
                if tools:
                    params["tools"] = tools
                    params["tool_choice"] = request_body.tool_choice

                # 非ストリーミングレスポンスを返す
                response = client.chat.completions.create(**params)

            return response

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"チャット完了エラー: {e}")
        logger.error(f"エラースタックトレース: {error_trace}")

        if isinstance(e, NotFoundError):
            return format_openai_error(
                status_code=404,
                message=f"指定されたモデルまたはエンドポイントが見つかりません: {str(e)}",
                type_="not_found_error"
            )
        elif isinstance(e, ValueError):
            return format_openai_error(
                status_code=400,
                message=str(e),
                type_="invalid_request_error"
            )

        return format_openai_error(
            status_code=500,
            message=f"チャット完了の処理中にエラーが発生しました: {str(e)}",
            type_="server_error"
        )

# テキスト補完API
@router.post("/v1/completions")
async def create_completion(
    request_body: CompletionRequest,
    request: Request,  # FastAPI依存注入用
    current_user = Depends(get_current_user)  # 認証用依存注入
):
    """テキスト補完APIエンドポイント

    OpenAI互換のテキスト補完APIを提供します。
    ストリーミングと非ストリーミングの両方のレスポンスをサポートします。
    """
    # 未使用パラメータの警告を抑制
    _ = request, current_user
    logger.info(f"テキスト補完リクエスト: model={request_body.model}, stream={request_body.stream}")

    # リクエスト情報を詳細にログに記録
    try:
        logger.info(f"リクエストURL: {request.url}")
        logger.info(f"リクエストメソッド: {request.method}")
        logger.info(f"リクエストヘッダー: {dict(request.headers)}")
        logger.info(f"リクエストクライアント: {request.client}")

        # プロンプトをログに出力
        logger.info(f"プロンプト: {request_body.prompt[:100]}...")

        if request_body.stream:
            # 適切なバックエンドクライアントを取得
            client = get_client(model=request_body.model)
            if not client:
                raise ValueError(f"モデル {request_body.model} に対応するクライアントが見つかりません")

            # バックエンドタイプを取得
            backend_type = get_backend_type(request_body.model)

            # vLLMバックエンドの場合は直接vLLMサーバーにリクエストを送信
            if backend_type == BackendType.VLLM:
                logger.info(f"vLLMバックエンドを使用します（直接APIを呼び出し、テキスト補完ストリーミング）")

                # vLLMサーバーに直接ストリーミングリクエストを送信
                async def vllm_completion_stream_generator():
                    try:
                        async for chunk in vllm_stream_completion(
                            model=request_body.model,
                            prompt=request_body.prompt,
                            temperature=request_body.temperature,
                            max_tokens=request_body.max_tokens,
                            top_p=request_body.top_p
                        ):
                            # ストリーミングチャンクをフィルタリング（特殊トークンの削除）
                            from ..utils.response_filter import filter_streaming_chunk
                            filtered_chunk = filter_streaming_chunk(chunk, is_chat_completion=False)
                            yield f"data: {json.dumps(filtered_chunk)}\n\n"
                        yield "data: [DONE]\n\n"
                    except Exception as e:
                        logger.error(f"vLLMストリーミング中エラー: {e}")
                        yield f"data: {json.dumps({'error': str(e)})}\n\n"

                # ストリーミングレスポンスを返す
                return StreamingResponse(
                    vllm_completion_stream_generator(),
                    media_type="text/event-stream"
                )
            else:
                # その他のバックエンドの場合はOpenAIクライアントを使用
                # ストリーム形式でレスポンス
                async def completion_streamer():
                    try:
                        stream = client.completions.create(
                            model=request_body.model,
                            prompt=request_body.prompt,
                            temperature=request_body.temperature,
                            max_tokens=request_body.max_tokens,
                            top_p=request_body.top_p,
                            stream=True
                        )

                        async for chunk in stream:
                            if hasattr(chunk, 'model_dump'):
                                chunk_data = chunk.model_dump()
                            else:
                                # 非標準形式の場合の処理
                                chunk_data = {
                                    "id": getattr(chunk, "id", str(uuid.uuid4())),
                                    "object": "text_completion",
                                    "created": int(time.time()),
                                    "model": request_body.model,
                                    "choices": [
                                        {
                                            "text": getattr(chunk, "text", ""),
                                            "index": 0,
                                            "finish_reason": getattr(chunk, "finish_reason", None)
                                        }
                                    ]
                                }

                            # ストリーミングチャンクをフィルタリング（特殊トークンの削除）
                            filtered_chunk = filter_streaming_chunk(chunk_data, is_chat_completion=False)

                            yield f"data: {json.dumps(filtered_chunk)}\n\n"

                        yield "data: [DONE]\n\n"
                    except Exception as e:
                        logger.error(f"ストリーミングエラー: {e}")
                        logger.error(f"エラー詳細: {traceback.format_exc()}")
                        error_data = {"error": {"message": str(e), "type": "server_error"}}
                        yield f"data: {json.dumps(error_data)}\n\n"

                return StreamingResponse(completion_streamer(), media_type="text/event-stream")
        else:
            # バックエンドタイプを取得
            backend_type = get_backend_type(request_body.model)

            # vLLMバックエンドの場合は直接vLLMサーバーにリクエストを送信
            if backend_type == BackendType.VLLM:
                logger.info(f"vLLMバックエンドを使用します（直接APIを呼び出し、テキスト補完）")

                # vLLMサーバーに直接リクエストを送信
                response = await vllm_completion(
                    model=request_body.model,
                    prompt=request_body.prompt,
                    temperature=request_body.temperature,
                    max_tokens=request_body.max_tokens,
                    top_p=request_body.top_p
                )

                # レスポンスをフィルタリング（特殊トークンの削除と最初の有効な回答の選択）
                # 注意: vllm_completion内ですでにフィルタリングされているが、念のため再度フィルタリング
                response = filter_completion_response(response)

                # レスポンスの内容をログに出力
                if "choices" in response and len(response["choices"]) > 0:
                    if "text" in response["choices"][0]:
                        text = response["choices"][0]["text"]
                        logger.info(f"フィルタリング後のテキスト補完レスポンス内容: {text[:100]}...")
            else:
                # その他のバックエンドの場合はOpenAIクライアントを使用
                # 適切なバックエンドクライアントを取得
                client = get_client(model=request_body.model)
                if not client:
                    raise ValueError(f"モデル {request_body.model} に対応するクライアントが見つかりません")

                # 単発レスポンス
                response = await client.completions.create(
                    model=request_body.model,
                    prompt=request_body.prompt,
                    temperature=request_body.temperature,
                    max_tokens=request_body.max_tokens,
                    top_p=request_body.top_p,
                    stream=False
                )

            return response

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"テキスト補完エラー: {e}")
        logger.error(f"エラースタックトレース: {error_trace}")

        if isinstance(e, NotFoundError):
            return format_openai_error(
                status_code=404,
                message=f"指定されたモデルまたはエンドポイントが見つかりません: {str(e)}",
                type_="not_found_error"
            )
        elif isinstance(e, ValueError):
            return format_openai_error(
                status_code=400,
                message=str(e),
                type_="invalid_request_error"
            )

        return format_openai_error(
            status_code=500,
            message=f"テキスト補完の処理中にエラーが発生しました: {str(e)}",
            type_="server_error"
        )

# 埋め込みAPI
@router.post("/v1/embeddings")
async def create_embeddings(request: EmbeddingRequest, current_user = Depends(get_current_user)):  # 認証用依存注入
    """埋め込み生成APIエンドポイント

    テキストの埋め込みベクトルを生成します。
    """
    # 未使用パラメータの警告を抑制
    _ = current_user
    logger.info(f"埋め込みリクエスト: model={request.model}")

    try:
        # 適切なバックエンドクライアントを取得
        client = get_client(model=request.model)

        # 埋め込みを生成
        response = client.embeddings.create(
            model=request.model,
            input=request.input,
            encoding_format=request.encoding_format,
            dimensions=request.dimensions
        )

        return response

    except Exception as e:
        logger.error(f"埋め込み生成エラー: {e}")
        return format_openai_error(
            status_code=500,
            message=f"埋め込み生成の処理中にエラーが発生しました: {str(e)}",
            type_="server_error"
        )

# 音声文字起こしAPI
@router.post("/v1/audio/transcriptions")
async def create_transcription(
    file: UploadFile = File(...),
    model: str = Form(...),
    prompt: Optional[str] = Form(None),
    response_format: Optional[str] = Form("json"),
    temperature: Optional[float] = Form(0.0),
    language: Optional[str] = Form(None),
    current_user = Depends(get_current_user)  # 認証用依存注入
):
    """音声文字起こしAPIエンドポイント

    音声ファイルからテキストへの変換を行います。
    """
    # 未使用パラメータの警告を抑制
    _ = current_user
    logger.info(f"音声文字起こしリクエスト: model={model}, file={file.filename}")

    try:
        # 適切なバックエンドクライアントを取得
        client = get_client(model=model)

        # 一時ファイルに保存
        temp_file_path = f"/tmp/{uuid.uuid4()}_{file.filename}"
        with open(temp_file_path, "wb") as temp_file:
            content = await file.read()
            temp_file.write(content)

        # 文字起こしを実行
        with open(temp_file_path, "rb") as audio_file:
            response = client.audio.transcriptions.create(
                model=model,
                file=audio_file,
                prompt=prompt,
                response_format=response_format,
                temperature=temperature,
                language=language
            )

        # 一時ファイルを削除
        os.remove(temp_file_path)

        return response

    except Exception as e:
        logger.error(f"音声文字起こしエラー: {e}")
        # 一時ファイルが存在する場合は削除
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        return format_openai_error(
            status_code=500,
            message=f"音声文字起こしの処理中にエラーが発生しました: {str(e)}",
            type_="server_error"
        )

# 画像生成API
@router.post("/v1/images/generations")
async def create_image(request: ImageGenerationRequest, current_user = Depends(get_current_user)):  # 認証用依存注入
    """画像生成APIエンドポイント

    テキストプロンプトから画像を生成します。
    """
    # 未使用パラメータの警告を抑制
    _ = current_user
    logger.info(f"画像生成リクエスト: model={request.model}, prompt={request.prompt[:20]}...")

    try:
        # 適切なバックエンドクライアントを取得
        client = get_client(model=request.model)

        # 画像を生成
        response = client.images.generate(
            model=request.model,
            prompt=request.prompt,
            n=request.n,
            size=request.size,
            response_format=request.response_format,
            style=request.style,
            quality=request.quality,
            user=request.user
        )

        return response

    except Exception as e:
        logger.error(f"画像生成エラー: {e}")
        return format_openai_error(
            status_code=500,
            message=f"画像生成の処理中にエラーが発生しました: {str(e)}",
            type_="server_error"
        )

# モデレーションAPI
@router.post("/v1/moderations")
async def create_moderation(request: ModerationRequest, current_user = Depends(get_current_user)):  # 認証用依存注入
    """モデレーションAPIエンドポイント

    コンテンツが不適切かどうかを判断します。
    """
    # 未使用パラメータの警告を抑制
    _ = current_user
    logger.info(f"モデレーションリクエスト: model={request.model}")

    try:
        # 適切なバックエンドクライアントを取得
        client = get_client(model=request.model)

        # モデレーションを実行
        response = client.moderations.create(
            input=request.input,
            model=request.model
        )

        return response

    except Exception as e:
        logger.error(f"モデレーションエラー: {e}")
        return format_openai_error(
            status_code=500,
            message=f"モデレーションの処理中にエラーが発生しました: {str(e)}",
            type_="server_error"
        )

# デバッグ用のルート情報取得API
@router.get("/v1/debug/routes")
async def get_routes():
    """デバッグ用：登録されているすべてのルートを表示します。"""
    from fastapi import FastAPI
    import inspect

    # 親アプリケーションを取得
    app = None
    frame = inspect.currentframe()
    while frame:
        if 'app' in frame.f_locals and isinstance(frame.f_locals['app'], FastAPI):
            app = frame.f_locals['app']
            break
        frame = frame.f_back

    if not app:
        # アプリケーションが見つからない場合は、ルーターの情報のみを返す
        routes = [{
            "path": route.path,
            "name": route.name,
            "methods": list(route.methods)
        } for route in router.routes]
        return {"router_routes": routes, "app_routes": "Not available"}

    # すべてのルートを取得
    routes = [{
        "path": route.path,
        "name": route.name,
        "methods": list(route.methods) if hasattr(route, 'methods') else []
    } for route in app.routes]

    return {"app_routes": routes}
