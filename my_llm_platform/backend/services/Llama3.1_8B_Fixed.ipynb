import torch
from unsloth import FastLanguageModel, PatchFastRL
PatchFastRL("GRPO", FastLanguageModel)

messages = [
    {"role": "system", "content": "You are a pirate chatbot who always responds in pirate speak!"},
    {"role": "user", "content": "Who are you?"},
]

from unsloth import FastLanguageModel
import torch
# 适合 RTX 3060 的优化参数
max_seq_length = 256  # 降低显存消耗
lora_rank = 16  # LoRA 低秩训练，更省显存

# 加载 Llama 3.1 8B - 禁用 vLLM 以解决内存问题
model, tokenizer = FastLanguageModel.from_pretrained(  dtype=torch.float16,
    model_name = "meta-llama/Llama-3.2-3B-Instruct", 
    max_seq_length = max_seq_length, 
    load_in_4bit = True,  # 4bit 量化，减少 75% 显存
    fast_inference = True,  # 禁用 vLLM 加速以避免内存错误
    max_lora_rank = lora_rank,
    gpu_memory_utilization = 0.9,  # 增加显存使用率
)

# # 首先，将模型转换为 float16
# model = model.to(torch.float16)
# 如果使用了 LoRA，合并 LoRA 权重
# if hasattr(model, "merge_and_unload"):
#     model = model.merge_and_unload()
    
# # 优化后模型存储
# model.save_pretrained("./optimized_model",safe_serialization=False)
# tokenizer.save_pretrained("./optimized_model")

from transformers import pipeline

pipe = pipeline(
    "text-generation",
    model=model,
    tokenizer=tokenizer,
    max_new_tokens=512,
    temperature=0.8,
    top_p=0.95
)

import torch
from transformers import pipeline

model_id = "meta-llama/Llama-3.2-3B-Instruct"
pipe = pipeline(
    "text-generation",
    model=model_id,
    torch_dtype=torch.bfloat16,
    device_map="auto",
)




outputs = pipe(
    messages,
    max_new_tokens=256,
)
print(outputs[0]["generated_text"][-1])


# prompts = ["Summarize the paper on AI.", "What is reinforcement learning?"]
# for prompt in prompts:
#     result = pipe(prompt)
#     print(f"Prompt: {prompt}")
#     print(f"Answer: {result[0]['generated_text'][len(prompt):]}") 
#     print("\n" + "-"*50 + "\n")





import torch
from transformers import pipeline

model_id = "meta-llama/Llama-3.2-3B-Instruct"
pipe = pipeline(
    "text-generation",
    model=model_id,
    torch_dtype=torch.bfloat16,
    device_map="auto",
)
messages = [
    {"role": "system", "content": "You are a pirate chatbot who always responds in pirate speak!"},
    {"role": "user", "content": "Who are you?"},
]
outputs = pipe(
    messages,
    max_new_tokens=256,
)
print(outputs[0]["generated_text"][-1])


# from vllm import LLM, SamplingParams

# llm = LLM(
#      model="meta-llama/meta-Llama-3.1-8B-Instruct",  # Use original model path
#     tokenizer="meta-llama/meta-Llama-3.1-8B-Instruct",
#   # model="./optimized_model",
#   #         tokenizer="./optimized_model",
#           dtype="float16",
#           quantization="awq", 
#           gpu_memory_utilization=0.85,
#           enforce_eager=True,
#           # quantization="bitsandbytes"
#         )  # 使用之前Unsloth优化的模型
# sampling_params = SamplingParams(temperature=0.8, top_p=0.95, max_tokens=512)




# 推理示例
prompts = ["Summarize the paper on AI.", "What is reinforcement learning?"]
outputs = llm.generate(prompts, sampling_params)

# 输出结果
for output in outputs:
    print(f"Prompt: {output.prompt}")
    print(f"Answer: {output.outputs[0].text}\n")