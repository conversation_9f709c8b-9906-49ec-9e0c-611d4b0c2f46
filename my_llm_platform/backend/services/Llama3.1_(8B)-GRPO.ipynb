from unsloth import FastLanguageModel

# 适合 RTX 3060 的优化参数
max_seq_length = 384  # 降低显存消耗
lora_rank = 16  # LoRA 低秩训练，更省显存

# 加载 Llama 3.1 8B
model, tokenizer = FastLanguageModel.from_pretrained( 
    model_name = "meta-llama/meta-Llama-3.1-8B-Instruct", 
    max_seq_length = max_seq_length, 
    load_in_4bit = True,  # 4bit 量化，减少 75% 显存
    fast_inference = True,  # vLLM 加速
    max_lora_rank = lora_rank,
    gpu_memory_utilization = 0.9,  # 降低显存使用率
)

# 优化后模型存储
model.save_pretrained("./optimized_model")
tokenizer.save_pretrained("./optimized_model")

%%capture
# Skip restarting message in Colab
import sys; 
modules = list(sys.modules.keys())
for x in modules: 
    if "PIL" in x or "google" in x: 
        sys.modules.pop(x)
        print("Modules restarted." + x)
    else:
        print("Modules None." + x )
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
# pip install diffusers
# !pip install unsloth vllm
# !pip install --upgrade pillow

import torch;
from unsloth import FastLanguageModel, PatchFastRL
PatchFastRL("GRPO", FastLanguageModel)

from unsloth import is_bfloat16_supported
import torch

# 适合 RTX 3060 的优化参数
max_seq_length = 384  # 降低显存消耗
lora_rank = 16  # LoRA 低秩训练，更省显存

# 加载 Llama 3.1 8B
model, tokenizer = FastLanguageModel.from_pretrained( 
    model_name = "meta-llama/meta-Llama-3.1-8B-Instruct", 
    max_seq_length = max_seq_length, 
    load_in_4bit = True,  # 4bit 量化，减少 75% 显存
    fast_inference = True,  # vLLM 加速
    max_lora_rank = lora_rank,
    gpu_memory_utilization = 0.5,  # 降低显存使用率
)

# 设定 LoRA 训练
model = FastLanguageModel.get_peft_model(
    model,
    r = lora_rank,  # LoRA 低秩
    target_modules = [
        "gate_proj", "up_proj", "down_proj",  # 只训练 MLP 层
    ],
    lora_alpha = lora_rank, 
    use_gradient_checkpointing = "unsloth",  # 启用梯度检查点，减少显存
    random_state = 3407, 
)

print("优化后的 Llama 3.1 8B 训练参数已加载！")


import re
from datasets import load_dataset, Dataset

# Load and prep dataset
SYSTEM_PROMPT = """
Respond in the following format:
<reasoning>
...
</reasoning>
<answer>
...
</answer>
"""

XML_COT_FORMAT = """\
<reasoning>
{reasoning}
</reasoning>
<answer>
{answer}
</answer>
"""

def extract_xml_answer(text: str) -> str:
    answer = text.split("<answer>")[-1]
    answer = answer.split("</answer>")[0]
    return answer.strip()

def extract_hash_answer(text: str) -> str | None:
    if "####" not in text:
        return None
    return text.split("####")[1].strip()

# uncomment middle messages for 1-shot prompting
def get_gsm8k_questions(split = "train") -> Dataset:
    data = load_dataset('openai/gsm8k', 'main')[split] # type: ignore
    data = data.map(lambda x: { # type: ignore
        'prompt': [
            {'role': 'system', 'content': SYSTEM_PROMPT},
            {'role': 'user', 'content': x['question']}
        ],
        'answer': extract_hash_answer(x['answer'])
    }) # type: ignore
    return data # type: ignore

dataset = get_gsm8k_questions()

#帮我写一个方法 用来判断一个数是否是偶数

# Reward functions
def correctness_reward_func(prompts, completions, answer, **kwargs) -> list[float]:
    responses = [completion[0]['content'] for completion in completions]
    q = prompts[0][-1]['content']
    extracted_responses = [extract_xml_answer(r) for r in responses]
    print('-'*20, f"Question:\n{q}", f"\nAnswer:\n{answer[0]}", f"\nResponse:\n{responses[0]}", f"\nExtracted:\n{extracted_responses[0]}")
    return [2.0 if r == a else 0.0 for r, a in zip(extracted_responses, answer)]

def int_reward_func(completions, **kwargs) -> list[float]:
    responses = [completion[0]['content'] for completion in completions]
    extracted_responses = [extract_xml_answer(r) for r in responses]
    return [0.5 if r.isdigit() else 0.0 for r in extracted_responses]

def strict_format_reward_func(completions, **kwargs) -> list[float]:
    """Reward function that checks if the completion has a specific format."""
    pattern = r"^<reasoning>\n.*?\n</reasoning>\n<answer>\n.*?\n</answer>\n$"
    responses = [completion[0]["content"] for completion in completions]
    matches = [re.match(pattern, r) for r in responses]
    return [0.5 if match else 0.0 for match in matches]

def soft_format_reward_func(completions, **kwargs) -> list[float]:
    """Reward function that checks if the completion has a specific format."""
    pattern = r"<reasoning>.*?</reasoning>\s*<answer>.*?</answer>"
    responses = [completion[0]["content"] for completion in completions]
    matches = [re.match(pattern, r) for r in responses]
    return [0.5 if match else 0.0 for match in matches]

def count_xml(text) -> float:
    count = 0.0
    if text.count("<reasoning>\n") == 1:
        count += 0.125
    if text.count("\n</reasoning>\n") == 1:
        count += 0.125
    if text.count("\n<answer>\n") == 1:
        count += 0.125
        count -= len(text.split("\n</answer>\n")[-1])*0.001
    if text.count("\n</answer>") == 1:
        count += 0.125
        count -= (len(text.split("\n</answer>")[-1]) - 1)*0.001
    return count

def xmlcount_reward_func(completions, **kwargs) -> list[float]:
    contents = [completion[0]["content"] for completion in completions]
    return [count_xml(c) for c in contents]

from trl import GRPOConfig, GRPOTrainer
training_args = GRPOConfig(
    use_vllm = True, # use vLLM for fast inference!
    learning_rate = 5e-6,
    adam_beta1 = 0.9,
    adam_beta2 = 0.99,
    weight_decay = 0.1,
    warmup_ratio = 0.1,
    lr_scheduler_type = "cosine",
    optim = "paged_adamw_8bit",
    logging_steps = 1,
    bf16 = is_bfloat16_supported(),
    fp16 = not is_bfloat16_supported(),
    per_device_train_batch_size = 1,
    gradient_accumulation_steps = 1, # Increase to 4 for smoother training
    num_generations = 6, # Decrease if out of memory
    max_prompt_length = 256,
    max_completion_length = 200,
    # num_train_epochs = 1, # Set to 1 for a full training run
    max_steps = 250,
    save_steps = 250,
    max_grad_norm = 0.1,
    report_to = "none", # Can use Weights & Biases
    output_dir = "outputs",
)

trainer = GRPOTrainer(
    model = model,
    processing_class = tokenizer,
    reward_funcs = [
        xmlcount_reward_func,
        soft_format_reward_func,
        strict_format_reward_func,
        int_reward_func,
        correctness_reward_func,
    ],
    args = training_args,
    train_dataset = dataset,
)
trainer.train()

text = tokenizer.apply_chat_template([
    {"role" : "user", "content" : "Calculate pi."},
], tokenize = False, add_generation_prompt = True)

from vllm import SamplingParams
sampling_params = SamplingParams(
    temperature = 0.8,
    top_p = 0.95,
    max_tokens = 1024,
)
output = model.fast_generate(
    [text],
    sampling_params = sampling_params,
    lora_request = None,
)[0].outputs[0].text

output

model.save_lora("grpo_saved_lora")

text = tokenizer.apply_chat_template([
    {"role" : "system", "content" : SYSTEM_PROMPT},
    {"role" : "user", "content" : "Calculate pi."},
], tokenize = False, add_generation_prompt = True)

from vllm import SamplingParams
sampling_params = SamplingParams(
    temperature = 0.8,
    top_p = 0.95,
    max_tokens = 1024,
)
output = model.fast_generate(
    text,
    sampling_params = sampling_params,
    lora_request = model.load_lora("grpo_saved_lora"),
)[0].outputs[0].text

output

# # Merge to 16bit
# if False: model.save_pretrained_merged("model", tokenizer, save_method = "merged_16bit",)
# if False: model.push_to_hub_merged("hf/model", tokenizer, save_method = "merged_16bit", token = "")

# # Merge to 4bit
# if False: model.save_pretrained_merged("model", tokenizer, save_method = "merged_4bit",)
# if False: model.push_to_hub_merged("hf/model", tokenizer, save_method = "merged_4bit", token = "")

# Just LoRA adapters
if False: model.save_pretrained_merged("model", tokenizer, save_method = "lora",)
if False: model.push_to_hub_merged("hf/model", tokenizer, save_method = "lora", token = "")

# 保存到本地
model.save_pretrained_merged(
    "meta-Llama-3.1-8B-Instructed-Chat-GRPO-unsloth", 
    tokenizer, 
    save_method="lora"
)

# 上传到 Hugging Face Hub
model.push_to_hub_merged(
    "liushuang393/meta-Llama-3.1-8B-Instruct-Chat-GRPO-unsloth", 
    tokenizer, 
    save_method="lora", 
    token=""
)


# Save to 8bit Q8_0
if False: model.save_pretrained_gguf("model", tokenizer,)
# Remember to go to https://huggingface.co/settings/tokens for a token!
# And change hf to your username!
if False: model.push_to_hub_gguf("hf/model", tokenizer, token = "")

# Save to 16bit GGUF
if False: model.save_pretrained_gguf("model", tokenizer, quantization_method = "f16")
if False: model.push_to_hub_gguf("hf/model", tokenizer, quantization_method = "f16", token = "")

# Save to q4_k_m GGUF
if False: model.save_pretrained_gguf("model", tokenizer, quantization_method = "q4_k_m")
if False: model.push_to_hub_gguf("hf/model", tokenizer, quantization_method = "q4_k_m", token = "")

# Save to multiple GGUF options - much faster if you want multiple!
if False:
    model.push_to_hub_gguf(
        "hf/model", # Change hf to your username!
        tokenizer,
        quantization_method = ["q4_k_m", "q8_0", "q5_k_m",],
        token = "",
    )

from unsloth import FastLanguageModel
from transformers import AutoTokenizer

# 1️⃣ 设定模型路径 & Checkpoint
base_model = "meta-llama/meta-Llama-3.1-8B-Instruct"
checkpoint_path = "/mnt/d/pythonPJ/ai_learn/nb/outputs/checkpoint-250"  # 你的最新 Checkpoint 目录
adapter_name = "default"  # LoRA 适配器的名称（通常是 "default"）

# 2️⃣ 重新加载基础模型
model, tokenizer = FastLanguageModel.from_pretrained(
    model_name=base_model,
    max_seq_length=512,
    load_in_4bit=True,
    fast_inference=True,
    gpu_memory_utilization=0.8,  # ⬆️ 提高显存利用率
)

# 3️⃣ 重新加载 LoRA 适配器（注意 r 的值！）
model = FastLanguageModel.get_peft_model(
    model,
    r=32,  # LoRA Rank（必须和训练时相同，通常是 16 或 32）
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj",
    ],
)

# 4️⃣ 载入训练好的 LoRA 适配器
model.load_adapter(checkpoint_path, adapter_name=adapter_name)

print("✅ LoRA 适配器已成功加载！")


# Properly move everything to CPU
def move_to_cpu(model):
    # Move base model to CPU
    model = model.cpu()
    
    # If this is a PEFT model, ensure adapters are also on CPU
    if hasattr(model, 'base_model') and hasattr(model.base_model, 'peft_config'):
        for adapter_name in model.base_model.peft_config.keys():
            for module in model.base_model.modules():
                if hasattr(module, 'lora_A') and hasattr(module, 'lora_B'):
                    if hasattr(module.lora_A, 'weight') and module.lora_A.weight is not None:
                        module.lora_A.weight = module.lora_A.weight.cpu()
                    if hasattr(module.lora_B, 'weight') and module.lora_B.weight is not None:
                        module.lora_B.weight = module.lora_B.weight.cpu()
    
    return model

# First clear CUDA cache
import torch
import gc
torch.cuda.empty_cache()
gc.collect()

# Then properly move model to CPU
model = move_to_cpu(model)

# Now try saving
model.save_pretrained_gguf(
    "q4_k_m/meta-Llama-3.1-8B-Instruct-Chat-GRPO-unsloth-Q4_K_M", 
    tokenizer,
    quantization_method="q4_k_m"
)

# Force CPU execution for merging
import os
os.environ["CUDA_VISIBLE_DEVICES"] = ""
# Then run your save command

import torch
# Save to q4_k_m GGUF
# More aggressive memory clearing
import torch
import gc
# Force CPU execution for merging
import os
os.environ["CUDA_VISIBLE_DEVICES"] = ""
# Then run your save command
torch.cuda.empty_cache()
gc.collect()
torch.cuda.synchronize()
model = model.cpu()
model.save_pretrained_gguf("q4_k_m/meta-Llama-3.1-8B-Instruct-Chat-GRPO-unsloth-Q4_K_M", tokenizer,maximum_memory_usage="10GiB",quantization_method = "q4_k_m")#, 

model.push_to_hub_gguf("hf/meta-Llama-3.1-8B-Instruct-Chat-GRPO-unsloth-Q4_K_M", tokenizer, maximum_memory_usage="10GiB",token = "",quantization_method = "q4_k_m")
