# My LLM Platform

複数の推論バックエンド、認証システム、高度な機能をサポートする完全なローカル大規模言語モデルプラットフォーム。

## システム概要

My LLM Platform は、以下の主要機能を提供する完全な大規模言語モデルアプリケーションプラットフォームです：

- **複数バックエンドサポート**：vLLM、Ollama、X-Inference などの多様な推論エンジンに対応
- **OpenAI 互換 API**：OpenAI API と完全に互換性のあるインターフェースを提供
- **プラグインシステム**：RAG、Agent、MCP などの拡張機能をサポート
- **ユーザー認証**：Keycloak 認証システムを統合
- **多言語サポート**：英語、日本語、中国語のインターフェース
- **モダンなフロントエンド**：React ベースのレスポンシブチャットインターフェース

## システムアーキテクチャ

```
my_llm_platform/
├── backend/              # FastAPI バックエンド + LLM ビジネスロジック
│   ├── auth/             # 認証関連モジュール
│   ├── db/               # データベースモデルと管理
│   ├── inference/        # 推論エンジンアダプター
│   ├── plugins/          # プラグインシステム
│   └── api/              # API ルート
├── frontend/chat-ui/     # React チャットインターフェース
│   ├── src/              # フロントエンドソースコード
│   │   ├── components/   # UI コンポーネント
│   │   ├── services/     # API サービス
│   │   └── i18n/         # 国際化サポート
└── docker-compose.yml    # サービス構成設定
```

## 主要機能

### 1. 複数バックエンド推論サポート

プラットフォームは複数の推論バックエンドをサポートし、必要に応じて動的に切り替えることができます：

- **vLLM**：高性能推論エンジン、大規模デプロイメントに適しています
- **Ollama**：軽量ローカル推論、個人開発に適しています
- **X-Inference**：エンタープライズグレードの推論サービス
- **カスタムバックエンド**：拡張可能なバックエンドアダプターアーキテクチャ

### 2. プラグインシステム

プラットフォームには強力なプラグインシステムが組み込まれており、以下をサポートしています：

- **RAG (検索拡張生成)**：ベクトルデータベースから関連ドキュメントを検索し、LLM の回答を強化
- **Agent**：ReAct パターンのインテリジェントエージェントを実装し、ツールを使用して複雑な問題を解決
- **MCP (Model Control Protocol)**：標準化されたモデル制御プロトコル、ツール呼び出しをサポート

### 3. ユーザーエクスペリエンス

- **ストリーミングレスポンス**：生成コンテンツをリアルタイムで表示
- **チャット履歴**：会話履歴の保存と管理
- **多言語サポート**：英語、日本語、中国語のインターフェース
- **パラメータチューニング**：モデルパラメータプロファイルの作成と管理をサポート

## インストールガイド

### システム要件

- **Docker / Docker Compose**：サービスコンテナの実行用
- **Python >= 3.10**：バックエンド開発環境
- **Node.js >= 16**：フロントエンド開発環境

### Windows ユーザー推奨環境

Windows ユーザーには、より良いパフォーマンスと互換性を得るために、**WSL2 + Docker Desktop** の使用を強く推奨します。詳細な設定については[Windows 開発環境設定](#windows-開発環境設定)を参照してください。

### クイックスタート

1. **リポジトリのクローン**

```bash
git clone https://github.com/yourusername/my_llm_platform.git
cd my_llm_platform
```

2. **バックエンドサービスの起動**
vllmの場合
python -m vllm.entrypoints.openai.api_server --model facebook/opt-125m --port 8001 --max-num-seqs 32 --max-num-batched-tokens 512 --max-model-len 512

ollamaの場合
ollama serve

x-inferenceの場合（docker-compose.ymlを編集 pip install x-inference）
docker-compose up -d x-inference

sglangの場合（pip install sglang）
python -m sglang.launch_server --model-path /path/to/model --port 8002

```bash
# 必要なサービスコンテナを起動qdrant|weaviate
docker-compose up -d redis rabbitmq weaviate

# バックエンド依存関係のインストール
cd backend
pip install -r requirements.txt

# バックエンドサービスの起動
uvicorn backend.main:app --reload
```

3. **フロントエンドの起動**

```bash
cd frontend/chat-ui
npm install
npm run dev
```

4. **アプリケーションへのアクセス**

ブラウザで http://localhost:5173 にアクセス

## 使用ガイド

### 基本的な使用方法

1. ブラウザでフロントエンドアプリケーションを開く
2. 使用するモデルとバックエンドを選択
3. AI との対話を開始

### プラグインの使用

チャットインターフェースで、設定から異なるプラグインを有効にできます：

- **RAG プラグイン**：知識ベースから関連情報を自動的に検索
- **Agent プラグイン**：ツールを使用して複雑な問題を解決
- **MCP プラグイン**：構造化されたツール呼び出しをサポート

### API の使用

プラットフォームは OpenAI と互換性のある API を提供し、OpenAI クライアントライブラリを直接使用して呼び出すことができます：

```python
from openai import OpenAI

client = OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="dummy-api-key"  # 認証が不要な場合
)

response = client.chat.completions.create(
    model="llama3.2",  # 使用するモデル名
    messages=[
        {"role": "user", "content": "こんにちは、自己紹介をお願いします。"}
    ]
)

print(response.choices[0].message.content)
```

## 開発ガイド

### バックエンド開発

- **新しい推論バックエンドの追加**：`backend/inference` ディレクトリに新しいアダプターを実装
- **新しいプラグインの作成**：`backend/plugins` ディレクトリに新しいプラグイン実装を追加
- **API の拡張**：`backend/api` ディレクトリに新しいルートを追加

### フロントエンド開発

- **新しい UI コンポーネントの追加**：`frontend/chat-ui/src/components` ディレクトリに作成
- **新しい言語サポートの追加**：`frontend/chat-ui/src/i18n/locales` ディレクトリに翻訳ファイルを追加

## サービス管理

### サービスの起動

```bash
# すべてのサービスを起動
docker-compose up -d

# 特定のサービスを起動
docker-compose up -d redis qdrant
```

### ログの確認

```bash
# すべてのサービスのログを確認
docker-compose logs -f

# 特定のサービスのログを確認
docker-compose logs -f redis
```

### サービスの停止

```bash
# すべてのサービスを停止
docker-compose down

# すべてのサービスを停止し、すべてのデータを削除（注意して使用）
docker-compose down -v
```

## Windows 開発環境設定

### WSL2 のインストール

1. PowerShell（管理者）で実行：
   ```powershell
   wsl --install
   ```

2. インストール完了後、コンピュータを再起動

### Docker Desktop の設定

1. [Docker Desktop](https://www.docker.com/products/docker-desktop/) をインストール
2. 設定で WSL2 統合を有効にする：
   - 設定 → 「General」→ ✅ "Use the WSL 2 based engine"
   - 設定 → 「Resources > WSL Integration」→ ✅ Ubuntu を有効化

### WSL2 での開発

```bash
# WSL2 に入る
wsl

# WSL2 でプロジェクトをクローンして実行
cd ~
git clone https://github.com/yourusername/my_llm_platform.git
cd my_llm_platform
```

## よくある問題

### Redis 接続失敗

**問題**：Redis サービスに接続できない
**解決策**：Redis コンテナが起動していることを確認 `docker-compose up -d redis`

### モデル読み込み失敗

**問題**：指定されたモデルを読み込めない
**解決策**：モデル名が正しいか、バックエンドがそのモデルをサポートしているかを確認

### フロントエンドがバックエンドに接続できない

**問題**：フロントエンドがバックエンド API に接続できない
**解決策**：API ベース URL 設定とプロキシ設定を確認

## ライセンス

このプロジェクトは MIT ライセンスの下で提供されています。詳細は [LICENSE](LICENSE) ファイルを参照してください。

## 貢献ガイド

コードの貢献、問題の報告、改善提案を歓迎します。以下の手順に従ってください：

1. このリポジトリをフォーク
2. 機能ブランチを作成 (`git checkout -b feature/amazing-feature`)
3. 変更をコミット (`git commit -m 'Add some amazing feature'`)
4. ブランチにプッシュ (`git push origin feature/amazing-feature`)
5. Pull Request を開く
